package org.demo.demo.controller;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.stage.Stage;

import java.io.IOException;

public class HomeController {

    @FXML
    private Button startButton;

    @FXML
    private Button learnMoreButton;

    @FXML
    private Button importButton;

    @FXML
    private Button searchButton;

    @FXML
    private Button analyzeButton;

    @FXML
    public void initialize() {
        // Initialization code if needed
    }

    @FXML
    private void onStartButtonClick() {
        navigateToAddFile();
    }

    @FXML
    private void onLearnMoreButtonClick() {
        // Could open a help dialog or documentation
        System.out.println("Learn more clicked - could open help documentation");
    }

    @FXML
    private void onImportButtonClick() {
        navigateToAddFile();
    }

    @FXML
    private void onSearchButtonClick() {
        navigateToSearch();
    }

    @FXML
    private void onAnalyzeButtonClick() {
        navigateToAddFileManuel();
    }

    private void navigateToAddFile() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/org/demo/demo/addFile.fxml"));
            Parent root = loader.load();

            Stage stage = (Stage) startButton.getScene().getWindow();
            Scene scene = new Scene(root, stage.getScene().getWidth(), stage.getScene().getHeight());
            stage.setScene(scene);
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }

    private void navigateToSearch() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/org/demo/demo/Recherche.fxml"));
            Parent root = loader.load();

            Stage stage = (Stage) searchButton.getScene().getWindow();
            Scene scene = new Scene(root, stage.getScene().getWidth(), stage.getScene().getHeight());
            stage.setScene(scene);
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }

    private void navigateToAddFileManuel() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/org/demo/demo/addFileManuel.fxml"));
            Parent root = loader.load();

            Stage stage = (Stage) analyzeButton.getScene().getWindow();
            Scene scene = new Scene(root, stage.getScene().getWidth(), stage.getScene().getHeight());
            stage.setScene(scene);
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }
}
