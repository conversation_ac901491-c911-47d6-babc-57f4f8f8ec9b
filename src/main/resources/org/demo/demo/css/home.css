/* ======= Global Styles ======= */
.scroll-pane {
    -fx-background-color: transparent;
    -fx-background: transparent;
}

.scroll-pane .viewport {
    -fx-background-color: transparent;
}

.scroll-pane .scroll-bar {
    -fx-opacity: 0.5;
}

.main-container {
    -fx-background-color: linear-gradient(from 0% 0% to 100% 100%, #f8fafc, #e2e8f0);
    -fx-spacing: 0;
}

/* ======= Hero Section ======= */
.hero-section {
    -fx-min-height: 350;
    -fx-padding: 50 30 50 30;
    -fx-background-color: linear-gradient(from 0% 0% to 100% 100%, #667eea, #764ba2);
}

.hero-content {
    -fx-max-width: 800;
}

.hero-title {
    -fx-font-size: 36px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 2, 0, 0, 1);
}

.hero-subtitle {
    -fx-font-size: 18px;
    -fx-font-weight: 300;
    -fx-text-fill: rgba(255, 255, 255, 0.9);
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.hero-description {
    -fx-font-size: 18px;
    -fx-text-fill: rgba(255, 255, 255, 0.85);
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-line-spacing: 2;
}

/* ======= Hero Bubbles ======= */
.hero-bubble {
    -fx-opacity: 0.1;
}

.hero-bubble-1 {
    -fx-fill: white;
    -fx-translate-x: -200;
    -fx-translate-y: -100;
}

.hero-bubble-2 {
    -fx-fill: white;
    -fx-translate-x: 180;
    -fx-translate-y: 80;
}

.hero-bubble-3 {
    -fx-fill: white;
    -fx-translate-x: -150;
    -fx-translate-y: 120;
}

/* ======= Buttons ======= */
.primary-button {
    -fx-background-color: #0072BC;
    -fx-text-fill: white;
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-padding: 15 30;
    -fx-background-radius: 25;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.4), 8, 0, 0, 3);
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.primary-button:hover {
    -fx-background-color: #005a94;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.6), 12, 0, 0, 4);
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
}

.secondary-button {
    -fx-background-color: transparent;
    -fx-text-fill: white;
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-padding: 15 30;
    -fx-background-radius: 25;
    -fx-border-color: white;
    -fx-border-width: 2;
    -fx-border-radius: 25;
    -fx-cursor: hand;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.secondary-button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
}

/* ======= Features Section ======= */
.features-section {
    -fx-padding: 50 30;
    -fx-background-color: white;
}

.section-title {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-text-fill: #2d3748;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.section-subtitle {
    -fx-font-size: 18px;
    -fx-text-fill: #718096;
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.features-container {
    -fx-max-width: 1200;
}

/* ======= Feature Cards ======= */
.feature-card {
    -fx-background-color: white;
    -fx-padding: 25;
    -fx-background-radius: 15;
    -fx-border-radius: 15;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 20, 0.2, 0, 5);
    -fx-max-width: 240;
    -fx-min-width: 240;
    -fx-min-height: 280;
}

.feature-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.15), 25, 0.3, 0, 8);
    -fx-translate-y: -5;
}

.feature-icon-container {
    -fx-pref-width: 70;
    -fx-pref-height: 70;
}

.feature-icon-bg {
    -fx-opacity: 0.15;
}

.feature-icon-bg-1 {
    -fx-fill: #0072BC;
}

.feature-icon-bg-2 {
    -fx-fill: #00AEEF;
}

.feature-icon-bg-3 {
    -fx-fill: #667eea;
}

.feature-icon {
    -fx-icon-size: 28;
    -fx-icon-color: #0072BC;
}

.feature-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-text-fill: #2d3748;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.feature-description {
    -fx-font-size: 14px;
    -fx-text-fill: #718096;
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-line-spacing: 1;
}

.feature-button {
    -fx-background-color: #f7fafc;
    -fx-text-fill: #0072BC;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-padding: 10 20;
    -fx-background-radius: 20;
    -fx-border-color: #e2e8f0;
    -fx-border-width: 1;
    -fx-border-radius: 20;
    -fx-cursor: hand;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.feature-button:hover {
    -fx-background-color: #0072BC;
    -fx-text-fill: white;
    -fx-border-color: #0072BC;
}

/* ======= Stats Section ======= */
.stats-section {
    -fx-padding: 60 40;
    -fx-background-color: #f7fafc;
}

.stat-number {
    -fx-font-size: 32px;
    -fx-font-weight: bold;
    -fx-text-fill: #0072BC;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.stat-label {
    -fx-font-size: 14px;
    -fx-text-fill: #718096;
    -fx-font-weight: 600;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}
