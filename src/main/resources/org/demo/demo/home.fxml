<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<BorderPane xmlns:fx="http://javafx.com/fxml"
            fx:controller="org.demo.demo.controller.HomeController"
            stylesheets="@css/home.css">

    <top>
        <fx:include source="navbar.fxml"/>
    </top>

    <center>
        <ScrollPane fitToWidth="true" fitToHeight="true" styleClass="scroll-pane">
            <VBox styleClass="main-container">

                <!-- Creative Hero Section -->
                <StackPane styleClass="creative-hero">
                    <!-- Animated Background Elements -->
                    <Circle fx:id="floatingCircle1" radius="40" styleClass="floating-element floating-1"/>
                    <Circle fx:id="floatingCircle2" radius="25" styleClass="floating-element floating-2"/>
                    <Circle fx:id="floatingCircle3" radius="35" styleClass="floating-element floating-3"/>
                    <Circle fx:id="floatingCircle4" radius="20" styleClass="floating-element floating-4"/>
                    <Circle fx:id="floatingCircle5" radius="30" styleClass="floating-element floating-5"/>

                    <!-- Geometric Shapes -->
                    <Polygon fx:id="triangle1" styleClass="geometric-shape triangle-1" points="0,0,30,0,15,26"/>
                    <Rectangle fx:id="square1" width="50" height="50" styleClass="geometric-shape square-1"/>
                    <Rectangle fx:id="square2" width="30" height="30" styleClass="geometric-shape square-2"/>

                    <!-- Main Content -->
                    <VBox alignment="CENTER" spacing="25" styleClass="hero-main-content">
                        <VBox alignment="CENTER" spacing="15" styleClass="title-container">
                            <Label text="✨ KitChiffre ✨" styleClass="creative-title"/>
                            <Label text="L'avenir du chiffrage intelligent" styleClass="creative-subtitle"/>
                            <Rectangle width="100" height="4" styleClass="title-underline"/>
                        </VBox>

                        <VBox alignment="CENTER" spacing="15" styleClass="description-container">
                            <Label text="🚀 Révolutionnez votre chiffrage" styleClass="hero-tagline"/>
                            <Label text="Solution moderne et intuitive pour vos projets"
                                   wrapText="true" maxWidth="450" styleClass="hero-description"/>
                        </VBox>

                        <HBox spacing="25" alignment="CENTER" styleClass="action-buttons">
                            <Button fx:id="startButton" styleClass="creative-primary-btn" onAction="#onStartButtonClick">
                                <graphic>
                                    <HBox spacing="8" alignment="CENTER">
                                        <FontIcon iconLiteral="bi-play-fill" styleClass="btn-icon"/>
                                        <Label text="Démarrer" styleClass="btn-text"/>
                                    </HBox>
                                </graphic>
                            </Button>
                            <Button fx:id="learnMoreButton" styleClass="creative-secondary-btn" onAction="#onLearnMoreButtonClick">
                                <graphic>
                                    <HBox spacing="8" alignment="CENTER">
                                        <FontIcon iconLiteral="bi-info-circle" styleClass="btn-icon"/>
                                        <Label text="Découvrir" styleClass="btn-text"/>
                                    </HBox>
                                </graphic>
                            </Button>
                        </HBox>
                    </VBox>
                </StackPane>

                <!-- Creative Features Grid -->
                <VBox styleClass="creative-features-section" spacing="30">
                    <VBox alignment="CENTER" spacing="15" styleClass="section-header">
                        <Label text="🎯 Fonctionnalités Principales" styleClass="creative-section-title"/>
                        <Label text="Outils puissants pour optimiser votre chiffrage"
                               styleClass="creative-section-subtitle" wrapText="true" maxWidth="500"/>
                        <Rectangle width="80" height="3" styleClass="section-underline"/>
                    </VBox>

                    <!-- Organized Feature Cards -->
                    <HBox spacing="25" alignment="CENTER" styleClass="features-grid">
                        <!-- Import Feature -->
                        <VBox styleClass="modern-feature-card" spacing="20" alignment="CENTER">
                            <StackPane styleClass="modern-icon-container">
                                <Circle radius="40" styleClass="modern-icon-bg modern-bg-1"/>
                                <FontIcon iconLiteral="bi-upload" styleClass="modern-icon" style="-fx-icon-color: #FF6B6B;"/>
                            </StackPane>
                            <VBox alignment="CENTER" spacing="10">
                                <Label text="Gestion des Fichiers" styleClass="modern-feature-title"/>
                                <Label text="Importez vos fichiers Excel et PDF facilement"
                                       styleClass="modern-feature-description" wrapText="true" maxWidth="230"/>
                            </VBox>
                            <Button fx:id="importButton" text="Gérer les Fichiers" styleClass="modern-feature-button" onAction="#onImportButtonClick"/>
                        </VBox>

                        <!-- Search Feature -->
                        <VBox styleClass="modern-feature-card" spacing="20" alignment="CENTER">
                            <StackPane styleClass="modern-icon-container">
                                <Circle radius="40" styleClass="modern-icon-bg modern-bg-2"/>
                                <FontIcon iconLiteral="bi-search" styleClass="modern-icon" style="-fx-icon-color: #4ECDC4;"/>
                            </StackPane>
                            <VBox alignment="CENTER" spacing="10">
                                <Label text="Recherche Intelligente" styleClass="modern-feature-title"/>
                                <Label text="Trouvez vos données rapidement et efficacement"
                                       styleClass="modern-feature-description" wrapText="true" maxWidth="200"/>
                            </VBox>
                            <Button fx:id="searchButton" text="Accéder aux Données" styleClass="modern-feature-button" onAction="#onSearchButtonClick"/>
                        </VBox>

                        <!-- Manual Entry Feature -->
                        <VBox styleClass="modern-feature-card" spacing="20" alignment="CENTER">
                            <StackPane styleClass="modern-icon-container">
                                <Circle radius="40" styleClass="modern-icon-bg modern-bg-3"/>
                                <FontIcon iconLiteral="bi-pencil" styleClass="modern-icon" style="-fx-icon-color: #FFB347;"/>
                            </StackPane>
                            <VBox alignment="CENTER" spacing="10">
                                <Label text="Saisie Manuelle" styleClass="modern-feature-title"/>
                                <Label text="Créez et modifiez vos données facilement"
                                       styleClass="modern-feature-description" wrapText="true" maxWidth="200"/>
                            </VBox>
                            <Button fx:id="analyzeButton" text="Saisie Directe" styleClass="modern-feature-button" onAction="#onAnalyzeButtonClick"/>
                        </VBox>
                    </HBox>
                </VBox>

                <!-- Creative Stats Dashboard -->
                <StackPane styleClass="stats-dashboard">
                    <!-- Background Pattern -->
                    <GridPane styleClass="stats-pattern">
                        <!-- Pattern elements will be styled in CSS -->
                    </GridPane>

                    <HBox spacing="30" alignment="CENTER" styleClass="stats-container">
                        <VBox alignment="CENTER" spacing="5" styleClass="stat-card">
                            <StackPane styleClass="stat-icon-container">
                                <Circle radius="20" styleClass="stat-icon-bg stat-bg-1"/>
                                <FontIcon iconLiteral="bi-shield" styleClass="stat-icon" style="-fx-icon-color: white;"/>
                            </StackPane>
                            <Label text="100%" styleClass="creative-stat-number"/>
                            <Label text="Sécurisé" styleClass="creative-stat-label"/>
                        </VBox>

                        <VBox alignment="CENTER" spacing="5" styleClass="stat-card">
                            <StackPane styleClass="stat-icon-container">
                                <Circle radius="20" styleClass="stat-icon-bg stat-bg-2"/>
                                <FontIcon iconLiteral="bi-clock" styleClass="stat-icon" style="-fx-icon-color: white;"/>
                            </StackPane>
                            <Label text="24/7" styleClass="creative-stat-number"/>
                            <Label text="Disponible" styleClass="creative-stat-label"/>
                        </VBox>

                        <VBox alignment="CENTER" spacing="5" styleClass="stat-card">
                            <StackPane styleClass="stat-icon-container">
                                <Circle radius="20" styleClass="stat-icon-bg stat-bg-3"/>
                                <FontIcon iconLiteral="bi-arrow-repeat" styleClass="stat-icon" style="-fx-icon-color: white;"/>
                            </StackPane>
                            <Label text="∞" styleClass="creative-stat-number"/>
                            <Label text="Possibilités" styleClass="creative-stat-label"/>
                        </VBox>

                        <VBox alignment="CENTER" spacing="5" styleClass="stat-card">
                            <StackPane styleClass="stat-icon-container">
                                <Circle radius="20" styleClass="stat-icon-bg stat-bg-4"/>
                                <FontIcon iconLiteral="bi-speedometer" styleClass="stat-icon" style="-fx-icon-color: white;"/>
                            </StackPane>
                            <Label text="⚡" styleClass="creative-stat-number"/>
                            <Label text="Ultra-Rapide" styleClass="creative-stat-label"/>
                        </VBox>
                    </HBox>
                </StackPane>

            </VBox>
        </ScrollPane>
    </center>
</BorderPane>
