<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<BorderPane xmlns:fx="http://javafx.com/fxml"
            fx:controller="org.demo.demo.controller.HomeController"
            stylesheets="@css/home.css">

    <top>
        <fx:include source="navbar.fxml"/>
    </top>

    <center>
        <ScrollPane fitToWidth="true" fitToHeight="true" styleClass="scroll-pane">
            <VBox styleClass="main-container">

                <!-- Creative Hero Section -->
                <StackPane styleClass="creative-hero">
                    <!-- Animated Background Elements -->
                    <Circle fx:id="floatingCircle1" radius="40" styleClass="floating-element floating-1"/>
                    <Circle fx:id="floatingCircle2" radius="25" styleClass="floating-element floating-2"/>
                    <Circle fx:id="floatingCircle3" radius="35" styleClass="floating-element floating-3"/>
                    <Circle fx:id="floatingCircle4" radius="20" styleClass="floating-element floating-4"/>
                    <Circle fx:id="floatingCircle5" radius="30" styleClass="floating-element floating-5"/>

                    <!-- Geometric Shapes -->
                    <Polygon fx:id="triangle1" styleClass="geometric-shape triangle-1" points="0,0 30,0 15,26"/>
                    <Rectangle fx:id="square1" width="50" height="50" styleClass="geometric-shape square-1"/>
                    <Rectangle fx:id="square2" width="30" height="30" styleClass="geometric-shape square-2"/>

                    <!-- Main Content -->
                    <VBox alignment="CENTER" spacing="40" styleClass="hero-main-content">
                        <VBox alignment="CENTER" spacing="20" styleClass="title-container">
                            <Label text="✨ KitChiffre ✨" styleClass="creative-title"/>
                            <Label text="L'avenir du chiffrage intelligent" styleClass="creative-subtitle"/>
                            <Rectangle width="100" height="4" styleClass="title-underline"/>
                        </VBox>

                        <VBox alignment="CENTER" spacing="15" styleClass="description-container">
                            <Label text="🚀 Révolutionnez votre processus de chiffrage" styleClass="hero-tagline"/>
                            <Label text="Une expérience utilisateur moderne, intuitive et puissante"
                                   wrapText="true" maxWidth="500" styleClass="hero-description"/>
                        </VBox>

                        <HBox spacing="25" alignment="CENTER" styleClass="action-buttons">
                            <Button fx:id="startButton" styleClass="creative-primary-btn" onAction="#onStartButtonClick">
                                <graphic>
                                    <HBox spacing="8" alignment="CENTER">
                                        <FontIcon iconLiteral="bi-rocket-takeoff" styleClass="btn-icon"/>
                                        <Label text="Démarrer" styleClass="btn-text"/>
                                    </HBox>
                                </graphic>
                            </Button>
                            <Button fx:id="learnMoreButton" styleClass="creative-secondary-btn" onAction="#onLearnMoreButtonClick">
                                <graphic>
                                    <HBox spacing="8" alignment="CENTER">
                                        <FontIcon iconLiteral="bi-lightbulb" styleClass="btn-icon"/>
                                        <Label text="Découvrir" styleClass="btn-text"/>
                                    </HBox>
                                </graphic>
                            </Button>
                        </HBox>
                    </VBox>
                </StackPane>

                <!-- Creative Features Grid -->
                <VBox styleClass="creative-features-section" spacing="50">
                    <VBox alignment="CENTER" spacing="15" styleClass="section-header">
                        <Label text="🎯 Nos Super-Pouvoirs" styleClass="creative-section-title"/>
                        <Label text="Des fonctionnalités qui transforment votre façon de travailler"
                               styleClass="creative-section-subtitle" wrapText="true" maxWidth="600"/>
                        <Rectangle width="80" height="3" styleClass="section-underline"/>
                    </VBox>

                    <!-- Hexagonal Feature Layout -->
                    <StackPane styleClass="hexagon-container">
                        <!-- Central Feature -->
                        <VBox styleClass="central-feature" spacing="15" alignment="CENTER">
                            <StackPane styleClass="central-icon-container">
                                <Circle radius="50" styleClass="central-icon-bg"/>
                                <FontIcon iconLiteral="bi-gear-fill" styleClass="central-icon"/>
                            </StackPane>
                            <Label text="KitChiffre" styleClass="central-title"/>
                            <Label text="Hub Central" styleClass="central-subtitle"/>
                        </VBox>

                        <!-- Surrounding Features -->
                        <VBox styleClass="feature-hexagon feature-hex-1" spacing="12" alignment="CENTER">
                            <StackPane styleClass="hex-icon-container">
                                <Polygon styleClass="hex-bg hex-bg-1" points="30,0 45,15 30,30 15,30 0,15 15,0"/>
                                <FontIcon iconLiteral="bi-cloud-upload" styleClass="hex-icon"/>
                            </StackPane>
                            <Label text="Import" styleClass="hex-title"/>
                            <Label text="Intelligent" styleClass="hex-subtitle"/>
                            <Button fx:id="importButton" text="Accéder" styleClass="hex-button" onAction="#onImportButtonClick"/>
                        </VBox>

                        <VBox styleClass="feature-hexagon feature-hex-2" spacing="12" alignment="CENTER">
                            <StackPane styleClass="hex-icon-container">
                                <Polygon styleClass="hex-bg hex-bg-2" points="30,0 45,15 30,30 15,30 0,15 15,0"/>
                                <FontIcon iconLiteral="bi-search-heart" styleClass="hex-icon"/>
                            </StackPane>
                            <Label text="Recherche" styleClass="hex-title"/>
                            <Label text="Avancée" styleClass="hex-subtitle"/>
                            <Button fx:id="searchButton" text="Explorer" styleClass="hex-button" onAction="#onSearchButtonClick"/>
                        </VBox>

                        <VBox styleClass="feature-hexagon feature-hex-3" spacing="12" alignment="CENTER">
                            <StackPane styleClass="hex-icon-container">
                                <Polygon styleClass="hex-bg hex-bg-3" points="30,0 45,15 30,30 15,30 0,15 15,0"/>
                                <FontIcon iconLiteral="bi-magic" styleClass="hex-icon"/>
                            </StackPane>
                            <Label text="Création" styleClass="hex-title"/>
                            <Label text="Magique" styleClass="hex-subtitle"/>
                            <Button fx:id="analyzeButton" text="Créer" styleClass="hex-button" onAction="#onAnalyzeButtonClick"/>
                        </VBox>
                    </StackPane>
                </VBox>

                <!-- Creative Stats Dashboard -->
                <StackPane styleClass="stats-dashboard">
                    <!-- Background Pattern -->
                    <GridPane styleClass="stats-pattern">
                        <!-- Pattern elements will be styled in CSS -->
                    </GridPane>

                    <HBox spacing="80" alignment="CENTER" styleClass="stats-container">
                        <VBox alignment="CENTER" spacing="8" styleClass="stat-card">
                            <StackPane styleClass="stat-icon-container">
                                <Circle radius="30" styleClass="stat-icon-bg stat-bg-1"/>
                                <FontIcon iconLiteral="bi-shield-check" styleClass="stat-icon"/>
                            </StackPane>
                            <Label text="100%" styleClass="creative-stat-number"/>
                            <Label text="Sécurisé" styleClass="creative-stat-label"/>
                        </VBox>

                        <VBox alignment="CENTER" spacing="8" styleClass="stat-card">
                            <StackPane styleClass="stat-icon-container">
                                <Circle radius="30" styleClass="stat-icon-bg stat-bg-2"/>
                                <FontIcon iconLiteral="bi-clock" styleClass="stat-icon"/>
                            </StackPane>
                            <Label text="24/7" styleClass="creative-stat-number"/>
                            <Label text="Disponible" styleClass="creative-stat-label"/>
                        </VBox>

                        <VBox alignment="CENTER" spacing="8" styleClass="stat-card">
                            <StackPane styleClass="stat-icon-container">
                                <Circle radius="30" styleClass="stat-icon-bg stat-bg-3"/>
                                <FontIcon iconLiteral="bi-infinity" styleClass="stat-icon"/>
                            </StackPane>
                            <Label text="∞" styleClass="creative-stat-number"/>
                            <Label text="Possibilités" styleClass="creative-stat-label"/>
                        </VBox>

                        <VBox alignment="CENTER" spacing="8" styleClass="stat-card">
                            <StackPane styleClass="stat-icon-container">
                                <Circle radius="30" styleClass="stat-icon-bg stat-bg-4"/>
                                <FontIcon iconLiteral="bi-lightning" styleClass="stat-icon"/>
                            </StackPane>
                            <Label text="⚡" styleClass="creative-stat-number"/>
                            <Label text="Ultra-Rapide" styleClass="creative-stat-label"/>
                        </VBox>
                    </HBox>
                </StackPane>

            </VBox>
        </ScrollPane>
    </center>
</BorderPane>
