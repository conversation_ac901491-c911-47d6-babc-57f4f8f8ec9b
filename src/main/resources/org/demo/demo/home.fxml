<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<BorderPane xmlns:fx="http://javafx.com/fxml"
            fx:controller="org.demo.demo.controller.HomeController"
            stylesheets="@css/home.css">

    <top>
        <fx:include source="navbar.fxml"/>
    </top>

    <center>
        <ScrollPane fitToWidth="true" fitToHeight="true" styleClass="scroll-pane">
            <VBox styleClass="main-container">

                <!-- Hero Section -->
                <StackPane styleClass="hero-section">
                    <!-- Background decorative elements -->
                    <Circle fx:id="heroBubble1" radius="120" styleClass="hero-bubble hero-bubble-1"/>
                    <Circle fx:id="heroBubble2" radius="80" styleClass="hero-bubble hero-bubble-2"/>
                    <Circle fx:id="heroBubble3" radius="60" styleClass="hero-bubble hero-bubble-3"/>

                    <VBox alignment="CENTER" spacing="30" styleClass="hero-content">
                        <VBox alignment="CENTER" spacing="15">
                            <Label text="KitChiffre" styleClass="hero-title"/>
                            <Label text="Application de Chiffrage des Kits" styleClass="hero-subtitle"/>
                        </VBox>

                        <Label text="Simplifiez votre travail de chiffrage avec une solution moderne et efficace"
                               wrapText="true" maxWidth="600" styleClass="hero-description"/>

                        <HBox spacing="20" alignment="CENTER">
                            <Button fx:id="startButton" text="Commencer" styleClass="primary-button" onAction="#onStartButtonClick"/>
                            <Button fx:id="learnMoreButton" text="En savoir plus" styleClass="secondary-button" onAction="#onLearnMoreButtonClick"/>
                        </HBox>
                    </VBox>
                </StackPane>

                <!-- Features Section -->
                <VBox styleClass="features-section" spacing="40">
                    <VBox alignment="CENTER" spacing="10">
                        <Label text="Fonctionnalités Principales" styleClass="section-title"/>
                        <Label text="Découvrez les outils qui vous aideront à optimiser votre processus de chiffrage"
                               styleClass="section-subtitle" wrapText="true" maxWidth="700"/>
                    </VBox>

                    <HBox spacing="30" alignment="CENTER" styleClass="features-container">
                        <!-- Import Feature -->
                        <VBox styleClass="feature-card" spacing="20" alignment="CENTER">
                            <StackPane styleClass="feature-icon-container">
                                <Circle radius="35" styleClass="feature-icon-bg feature-icon-bg-1"/>
                                <FontIcon iconLiteral="bi-file-earmark-arrow-up" styleClass="feature-icon"/>
                            </StackPane>
                            <VBox alignment="CENTER" spacing="8">
                                <Label text="Gestion des Fichiers" styleClass="feature-title"/>
                                <Label text="Importez et gérez vos documents Excel et PDF de manière sécurisée"
                                       styleClass="feature-description" wrapText="true" maxWidth="200"/>
                            </VBox>
                            <Button fx:id="importButton" text="Gérer les Fichiers" styleClass="feature-button" onAction="#onImportButtonClick"/>
                        </VBox>

                        <!-- Analysis Feature -->
                        <VBox styleClass="feature-card" spacing="20" alignment="CENTER">
                            <StackPane styleClass="feature-icon-container">
                                <Circle radius="35" styleClass="feature-icon-bg feature-icon-bg-2"/>
                                <FontIcon iconLiteral="bi-search" styleClass="feature-icon"/>
                            </StackPane>
                            <VBox alignment="CENTER" spacing="8">
                                <Label text="Recherche Intelligente" styleClass="feature-title"/>
                                <Label text="Accédez rapidement à vos données avec des filtres avancés"
                                       styleClass="feature-description" wrapText="true" maxWidth="200"/>
                            </VBox>
                            <Button fx:id="searchButton" text="Accéder aux Données" styleClass="feature-button" onAction="#onSearchButtonClick"/>
                        </VBox>

                        <!-- Results Feature -->
                        <VBox styleClass="feature-card" spacing="20" alignment="CENTER">
                            <StackPane styleClass="feature-icon-container">
                                <Circle radius="35" styleClass="feature-icon-bg feature-icon-bg-3"/>
                                <FontIcon iconLiteral="bi-pencil-square" styleClass="feature-icon"/>
                            </StackPane>
                            <VBox alignment="CENTER" spacing="8">
                                <Label text="Saisie Manuelle" styleClass="feature-title"/>
                                <Label text="Créez et modifiez vos données directement dans l'application"
                                       styleClass="feature-description" wrapText="true" maxWidth="200"/>
                            </VBox>
                            <Button fx:id="analyzeButton" text="Saisie Directe" styleClass="feature-button" onAction="#onAnalyzeButtonClick"/>
                        </VBox>
                    </HBox>
                </VBox>

                <!-- Stats Section -->
                <HBox styleClass="stats-section" spacing="60" alignment="CENTER">
                    <VBox alignment="CENTER" spacing="5">
                        <Label text="100%" styleClass="stat-number"/>
                        <Label text="Sécurisé" styleClass="stat-label"/>
                    </VBox>
                    <VBox alignment="CENTER" spacing="5">
                        <Label text="24/7" styleClass="stat-number"/>
                        <Label text="Disponible" styleClass="stat-label"/>
                    </VBox>
                    <VBox alignment="CENTER" spacing="5">
                        <Label text="∞" styleClass="stat-number"/>
                        <Label text="Fichiers" styleClass="stat-label"/>
                    </VBox>
                    <VBox alignment="CENTER" spacing="5">
                        <Label text="⚡" styleClass="stat-number"/>
                        <Label text="Rapide" styleClass="stat-label"/>
                    </VBox>
                </HBox>

            </VBox>
        </ScrollPane>
    </center>
</BorderPane>
